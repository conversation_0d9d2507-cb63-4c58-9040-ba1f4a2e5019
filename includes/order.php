
<div id="notification" class="notification" style="display: none;">Order Sent Successfully!</div>

<div class="row_three">
  <div>
    <div class="row_one">
      <div class="row container-xs">
        <div class="rowquicksecure">
          <div class="columnquicksecu">
            <h1 class="quicksecure ui heading size-heading2xl">
            Safest &amp; Most Reliable <br> Auto Transport Services
            </h1>
            <p class="experience ui text size-textmd" style="text-align: center !important;">The quickest and easiest method to ship your car nationwide</p>
            <button class="flex-row-center-center get_an_instant-2" onclick="window.location.href='quote.php'">Get an instant quote</button>
          </div>



<form id="transportForm" method="POST" action="send-leads" class="stackarrowdown">
  <div class="quote-1">
    <h2 class="getaninstant ui heading size-headings" style="margin-left: 20px;">
      <span>Get an instant Quote Now</span>
      <span class="getaninstant-span">or call <a href="tel:8778788008" class="getaninstant-span-call"> (*************</a></span>
    </h2>

    <!-- Step 1: From/To form -->
    <div class="step" id="step-1">
      <div class="input-group" style="position: relative;">
        <div class="location-tooltip" id="location-tooltip">
          Begin typing a zip code or city and click a suggested location.
          <div class="tooltip-arrow"></div>
        </div>
        <div class="input-wrapper">
          <img src="public/images/Vector.svg" alt="Location Icon" class="icon" />
          <input type="text" name="transport_from" id="transport_from" class="from-input" placeholder="From (ZIP or City)" required />
        </div>
        <div class="input-wrapper">
          <img src="public/images/Vector-2.svg" alt="Location Icon" class="icon" />
          <input type="text" name="transport_to" id="transport_to" class="to-input" placeholder="To (ZIP or City)" required />
        </div>
      </div>
      <label class="transoprt-type">Transport Type</label>
      <div class="rowtransporttyp">
        <label class="ui checkbox">
          <input type="radio" name="transport_type" value="Open" required />
          <div></div><span>Open</span>
        </label>
        <label class="ui checkbox">
          <input type="radio" name="transport_type" value="Enclosed" required />
          <div></div><span>Enclosed</span>
        </label>
      </div>

      <button type="button" class="step-buttom" onclick="nextStep(2)">Vehicle Details</button>
    </div>

    <!-- Step 2: Vehicle details -->
    <div class="step" id="step-2" style="display: none;">
      <div class="input-group">
        <div class="select-custom">
          <select name="vehicle_year" id="vehicle_year" class="styled-select" style="padding-right: 40px;" required>
            <option value="" disabled selected>Vehicle year</option>
            <!-- Populate dynamically with JavaScript if needed -->
          </select>
          <span class="dropdown-icon"></span>
        </div>
        <div class="select-custom">
          <select name="vehicle_brand" id="vehicle_brand" class="styled-select" style="padding-right: 40px;" required>
            <option value="" disabled selected>Vehicle Make</option>
            <!-- Populate dynamically -->
          </select>
          <span class="dropdown-icon"></span>
        </div>
        <div class="select-custom">
          <select name="vehicle_model" id="vehicle_model" class="styled-select" style="padding-right: 40px;" required>
            <option value="" disabled selected>Vehicle model</option>
            <!-- Populate dynamically -->
          </select>
          <span class="dropdown-icon"></span>
        </div>
      </div>
      <label class="vehicle-type">Is it operable?</label>
      <div class="rowtransporttyp">
        <label class="ui checkbox">
          <input type="radio" name="vehicle_operable" value="yes" required />
          <div></div><span>Yes</span>
        </label>
        <label class="ui checkbox">
          <input type="radio" name="vehicle_operable" value="no" required />
          <div></div><span>No</span>
        </label>
      </div>

      <button type="button" class="step-buttom" onclick="nextStep(3)">Confirmation Details</button>
    </div>

    <!-- Step 3: Contact details -->
    <div class="step" id="step-3" style="display: none;">
      <div class="input-group">
        <div class="input-wrapper">
          <input type="email" name="email" id="email" class="info-input" placeholder="Your Email" required />
        </div>
        <div class="input-wrapper">
          <input type="text" name="available_date" id="available_date" class="info-input" placeholder="Ship Date" readonly required />
        </div>
        <div class="input-wrapper">
          <input type="text" name="name" id="name" class="info-input" placeholder="Your name" required />
        </div>
        <div class="input-wrapper">
          <input type="tel" name="phone" id="phone" class="info-input" placeholder="Your phone" required />
        </div>
      </div>
      <div class="step-indicators">
        <div class="indicator completed-step" id="step-indicator-1" onclick="goToStep(1)">
          <span class="indicator-number">1</span>
          <p>Destination</p>
        </div>
        <hr class="indicator-line">
        <div class="indicator completed-step" id="step-indicator-2" onclick="goToStep(2)">
          <span class="indicator-number">2</span>
          <p>Vehicle</p>
        </div>
        <hr class="indicator-line">
        <div class="indicator active-step" id="step-indicator-3">
          <span class="indicator-number">3</span>
          <p>Info</p>
        </div>
      </div>
      <button type="submit" class="step-buttom" id="finishButton">Get Quote</button>
    </div>
  </div>
</form>

        </div>
      </div>
    </div>
  </div>
</div>

<script>
    document.getElementById('vehicle_year').addEventListener('change', function () {
    document.getElementById('vehicle_brand').disabled = false;
});
</script>
<script src="https://code.jquery.com/jquery-3.6.0.min.js" data-cmp-ab="2"></script>
<link rel="stylesheet" href="https://code.jquery.com/ui/1.12.1/themes/base/jquery-ui.css" data-cmp-ab="2">
<script src="https://code.jquery.com/ui/1.12.1/jquery-ui.js" data-cmp-ab="2"></script>
<script>
// Global callback function for Google Maps API
function initGoogleMaps() {
    console.log('Google Maps API loaded successfully');
    if (document.getElementById('transport_from')) {
        initializeAutocomplete(document.getElementById('transport_from'));
    }
    if (document.getElementById('transport_to')) {
        initializeAutocomplete(document.getElementById('transport_to'));
    }
}

// Show/hide location tooltip on focus/blur
document.addEventListener('DOMContentLoaded', function() {
    const transportFrom = document.getElementById('transport_from');
    const transportTo = document.getElementById('transport_to');
    const locationTooltip = document.getElementById('location-tooltip');

    function showTooltip() {
        locationTooltip.classList.add('show');
    }

    function hideTooltip() {
        locationTooltip.classList.remove('show');
    }

    if (transportFrom) {
        transportFrom.addEventListener('focus', showTooltip);
        transportFrom.addEventListener('blur', hideTooltip);
    }

    if (transportTo) {
        transportTo.addEventListener('focus', showTooltip);
        transportTo.addEventListener('blur', hideTooltip);
    }
});
</script>
<script async defer src="https://maps.googleapis.com/maps/api/js?key=AIzaSyDPqmvF0Uf9aR1N1hQZVSUyibk__vkaegk&libraries=places&callback=initGoogleMaps" data-cmp-ab="2"></script>
<style>
  .input-wrapper.input-error {
    border: 1px solid #fb0000 !important;
  }

  .radio-error {
    border: 1px solid #fb0000 !important;
    border-radius: 10px;
    padding: 5px;
  }

  .location-tooltip {
    position: absolute;
    top: -60px;
    left: 0;
    right: 0;
    background-color: #424242;
    color: white;
    padding: 12px 16px;
    border-radius: 8px;
    font-size: 14px;
    line-height: 1.4;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease-in-out;
    transform: translateY(10px);
    z-index: 1000;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  .location-tooltip.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
  }

  .tooltip-arrow {
    position: absolute;
    bottom: -8px;
    left: 20px;
    width: 0;
    height: 0;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-top: 8px solid #424242;
  }
</style>
<script>
function initializeAutocomplete(inputElement) {
    const autocomplete = new google.maps.places.Autocomplete(inputElement, {
        types: ['(regions)'],
        componentRestrictions: { country: 'us' }
    });

    autocomplete.addListener('place_changed', function () {
        const place = autocomplete.getPlace();
        if (place.address_components) {
            let city = '';
            let state = '';
            let zip = '';

            place.address_components.forEach(component => {
                const types = component.types;
                if (types.includes('locality') || types.includes('postal_town')) {
                    city = component.long_name;
                }
                if (types.includes('administrative_area_level_1')) {
                    state = component.short_name;
                }
                if (types.includes('postal_code')) {
                    zip = component.long_name;
                }
            });

            const formatted = `${city}, ${state} ${zip}, USA`;
            $(inputElement).val(formatted);
        }
    });
}

// Google Maps API will be initialized via callback function
</script>
<script>
     // JavaScript to handle the multi-step form transitions
        // nextStep is now defined in the validation code below

        function goToStep(step) {
            document.querySelector('#step-' + (step - 1)).style.display = 'none';
            document.querySelector('#step-' + step).style.display = 'block';
        }
</script>



<script>
  // Initialize datepicker with custom styling
  $(function() {
    $("#available_date").datepicker({
      dateFormat: 'mm/dd/yy',
      minDate: 0, // Prevent selecting dates in the past
      showOtherMonths: true,
      selectOtherMonths: true,
      changeMonth: true,
      changeYear: true,
      yearRange: 'c:c+1', // Current year to next year
      beforeShow: function(input, inst) {
        // Add custom class to the datepicker
        setTimeout(function() {
          inst.dpDiv.addClass('custom-datepicker');
        }, 0);
      }
    });

    $("<style>")
      .prop("type", "text/css")
      .html(`
        .custom-datepicker {
          border: 1px solid #CDA565 !important;
          border-radius: 10px !important;
          font-family: 'Nohemi', sans-serif !important;
          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1) !important;
        }
        .custom-datepicker .ui-datepicker-header {
          background: #b68544 !important;
          color: #ffffff !important;
          border: none !important;
          border-radius: 8px 8px 0 0 !important;
        }
        .custom-datepicker .ui-datepicker-calendar thead th {
          color: #46351A !important;
          font-weight: 600 !important;
        }
        .custom-datepicker .ui-state-default {
          background: #F3F1F5 !important;
          border: 1px solid #EAD4B9 !important;
          color: #46351A !important;
          text-align: center !important;
        }
        .custom-datepicker .ui-state-hover {
          background: #EBE2D8 !important;
          color: #222A31 !important;
        }
        .custom-datepicker .ui-state-active {
          background: #CDA565 !important;
          color: #ffffff !important;
          font-weight: bold !important;
        }
        .custom-datepicker .ui-datepicker-today .ui-state-default {
          border: 2px solid #b58544 !important;
        }
        .custom-datepicker .ui-datepicker-prev,
        .custom-datepicker .ui-datepicker-next {
          background: #b58544 !important;
          border: none !important;
          cursor: pointer !important;
        }
        .custom-datepicker .ui-datepicker-prev span,
        .custom-datepicker .ui-datepicker-next span {
          filter: brightness(0) invert(1) !important;
        }
      `)
      .appendTo("head");
  });

  // Function to validate fields and highlight empty ones
  function validateFields(fieldIds) {
    let allValid = true;

    fieldIds.forEach(function(fieldId) {
      const field = document.getElementById(fieldId);
      const isRadio = field.type === 'radio';

      if (isRadio) {
        const isChecked = document.querySelector(`input[name="${field.name}"]:checked`);
        if (!isChecked) {
          // Highlight radio group container
          document.querySelector(`input[name="${field.name}"]`).closest(".rowtransporttyp").classList.add("radio-error");
          allValid = false;
        } else {
          document.querySelector(`input[name="${field.name}"]`).closest(".rowtransporttyp").classList.remove("radio-error");
        }
      } else {
        // For regular inputs
        if (field.value.trim() === '') {
          // Add error class to highlight the field
          field.closest(".input-wrapper").classList.add("input-error");
          allValid = false;
        } else {
          field.closest(".input-wrapper").classList.remove("input-error");
        }
      }
    });

    return allValid;
  }

  // Add validation to the date field specifically
  document.getElementById('available_date').addEventListener('change', function() {
    if (this.value.trim() === '') {
      this.closest(".input-wrapper").classList.add("input-error");
    } else {
      this.closest(".input-wrapper").classList.remove("input-error");
    }
  });

  // Validate before moving to next step
  function nextStep(step) {
    // If moving to step 3, validate step 2 fields
    if (step === 3) {
      const step2Fields = ['vehicle_year', 'vehicle_brand', 'vehicle_model', 'vehicle_operable'];
      if (!validateFields(step2Fields)) {
        alert('Please fill out all the required fields.');
        return;
      }
    }

    // If moving to step 2, validate step 1 fields
    if (step === 2) {
      const step1Fields = ['transport_from', 'transport_to', 'transport_type'];
      if (!validateFields(step1Fields)) {
        alert(' Please fill out all the required fields.');
        return;
      }
    }

    document.querySelector('#step-' + (step - 1)).style.display = 'none';
    document.querySelector('#step-' + step).style.display = 'block';
  }

  // Override the original nextStep function
  window.nextStep = nextStep;

  // Form submission validation
  document.getElementById('transportForm').addEventListener('submit', function(event) {
    // Prevent the default form submission first
    event.preventDefault();

    // Check specifically for the date field
    const dateField = document.getElementById('available_date');
    if (!dateField.value.trim()) {
      dateField.closest(".input-wrapper").classList.add("input-error");
      alert('Please select an available date.');
      return false;
    }

    // Check all other required fields
    const requiredFields = [
        'transport_from', 'transport_to', 'transport_type',
        'vehicle_year', 'vehicle_brand', 'vehicle_model',
        'vehicle_operable', 'email', 'available_date', 'name', 'phone'
    ];

    if (!validateFields(requiredFields)) {
      alert('Please fill out all the required fields.');
      return false;
    } else {
      // If all validations pass, submit the form
      alert('Your form has been successfully submitted!');
      this.submit();
    }
  });

  // Also add a click handler to the submit button as a backup
  document.getElementById('finishButton').addEventListener('click', function(event) {
    // The form submit event handler above will take care of validation
    // This is just a backup in case the form submit event doesn't fire
    const dateField = document.getElementById('available_date');
    if (!dateField.value.trim()) {
      dateField.closest(".input-wrapper").classList.add("input-error");
      alert('Please select an available date.');
      event.preventDefault();
    }
  });

</script>


<script type="text/javascript">
    document.addEventListener("DOMContentLoaded", function () {
        setTimeout(function () {
            let submitButton = document.getElementById("finishButton");
            let transportForm = document.getElementById("transportForm");

            if (submitButton && transportForm) {
                console.log("iubenda initialized:", submitButton, transportForm);

                _iub.cons_instructions.push(["load", {
                    submitElement: submitButton,
                    form: {
                    selector: transportForm,
                                map: {
                                    subject: {
                                        full_name: "name",
                                        // first_name: "name",
                                        email: "email",
                                        phones: "phone"
                                    },
                                    preferences: {
                                        transport_from: "transport_from",
                                        transport_to: "transport_to",
                                        transport_type: "transport_type",
                                        vehicle_year: "vehicle_year",
                                        vehicle_brand: "vehicle_brand",
                                        vehicle_model: "vehicle_model",
                                        available_date: "available_date"
                                    }
                                }
                            },
                            consent: {
                                legal_notices: [
                                    { identifier: "privacy_policy" },
                                    { identifier: "cookie_policy" },
                                    { identifier: "terms" }
                                ]
                            }
                        }]);
                    } else {
                        console.error("Error: Submit button or form not found.");
                    }
                }, 2000);
            });
</script>